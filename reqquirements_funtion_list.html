<!DOCTYPE html>

<html>
<head>
	
	<meta http-equiv="content-type" content="text/html; charset=utf-8"/>
	<title></title>
	<meta name="generator" content="LibreOffice 25.8.0.4 (Windows)"/>
	<meta name="created" content="2025-09-07T10:16:47"/>
	<meta name="changed" content="00:00:00"/>
	<meta name="AppVersion" content="15.0000"/>
	
	<style type="text/css">
		body,div,table,thead,tbody,tfoot,tr,th,td,p { font-family:"Noto Sans SC"; font-size:x-small }
		a.comment-indicator:hover + comment { background:#ffd; position:absolute; display:block; border:1px solid black; padding:0.5em;  } 
		a.comment-indicator { background:red; display:inline-block; border:1px solid black; width:0.5em; height:0.5em;  } 
		comment { display:none;  } 
	</style>
	
</head>

<body>
<table cellspacing="0" border="0">
	<colgroup span="2" width="141"></colgroup>
	<colgroup width="172"></colgroup>
	<colgroup width="747"></colgroup>
	<colgroup width="242"></colgroup>
	<tr>
		<td style="border-top: 1px solid #ffffff; border-bottom: 1px solid #ffffff; border-left: 1px solid #ffffff; border-right: 1px solid #ffffff" height="38" align="left" valign=middle bgcolor="#1F2329" data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;一级分类&quot;}"><font color="#FFFFFF">一级分类</font></td>
		<td style="border-top: 1px solid #ffffff; border-bottom: 1px solid #ffffff; border-left: 1px solid #ffffff; border-right: 1px solid #ffffff" align="left" valign=middle bgcolor="#1F2329" data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;模块&quot;}"><font color="#FFFFFF">模块</font></td>
		<td style="border-top: 1px solid #ffffff; border-bottom: 1px solid #ffffff; border-left: 1px solid #ffffff; border-right: 1px solid #ffffff" align="left" valign=middle bgcolor="#1F2329" data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;子模块&quot;}"><font color="#FFFFFF">子模块</font></td>
		<td style="border-top: 1px solid #ffffff; border-bottom: 1px solid #ffffff; border-left: 1px solid #ffffff; border-right: 1px solid #ffffff" align="left" valign=middle bgcolor="#1F2329" data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;功能清单&quot;}"><font color="#FFFFFF">功能清单</font></td>
		<td style="border-top: 1px solid #ffffff; border-bottom: 1px solid #ffffff; border-left: 1px solid #ffffff; border-right: 1px solid #ffffff" align="left" valign=middle bgcolor="#1F2329" data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;备注&quot;}"><font color="#FFFFFF">备注</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" rowspan=2 align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;首页&quot;}"><font color="#000000">首页</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" rowspan=2 align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;首页&quot;}"><font color="#1F2329">首页</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;首页配置模块：\nBanner：未注册引导注册、已注册引导提交企业认证的材料二级页；\n·产品入口\n·活动入口\n·干货入口\n·品牌介绍&quot;}"><font color="#1F2329">首页配置模块：<br>Banner：未注册引导注册、已注册引导提交企业认证的材料二级页；<br>·产品入口<br>·活动入口<br>·干货入口<br>·品牌介绍</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#1F2329"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;【管理后台】\n首页装修：支持配置图片、视频、轮播组件、富文本组件搭建首页\n跳转链接：支持跳转产品列表、活动列表、干货列表、注册页、自定义二级页\n模块配置项：支持控制首页模块可见范围：小程序已登录、小程序未登录&quot;}"><font color="#1F2329">【管理后台】<br>首页装修：支持配置图片、视频、轮播组件、富文本组件搭建首页<br>跳转链接：支持跳转产品列表、活动列表、干货列表、注册页、自定义二级页<br>模块配置项：支持控制首页模块可见范围：小程序已登录、小程序未登录</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#1F2329"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" rowspan=2 align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;产品&quot;}"><font color="#000000">产品</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;产品分类&quot;}"><font color="#000000">产品分类</font></td>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;产品列表：4个产品线类目：全球企业账户、全球支出管理、全球收单、平台API 和嵌入式金融；&quot;}"><font color="#000000">产品列表：4个产品线类目：全球企业账户、全球支出管理、全球收单、平台API 和嵌入式金融；</font></td>
		<td align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;根据更新频率及上线时间，讨论该模块可以前端直接开发？&quot;}"><font color="#1F2329">根据更新频率及上线时间，讨论该模块可以前端直接开发？</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;产品详情&quot;}"><font color="#000000">产品详情</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;1.产品范围：共计4个分类详情+15个产品详情，每个详情页根据官网适配手机情况做内容的删减；\n2.引导注册：已经绑定小程序的用户引导注册进行隐藏；\n3.视频模块：此处模块对接荟聚视频链接进行引用，点击播放，点击前部分视频需要做登录校验；\n4.其他官网内容（如API接口文档）、非本期范围内容、二级页多层跳转等场景的小程序进行隐藏。&quot;}"><font color="#000000">1.产品范围：共计4个分类详情+15个产品详情，每个详情页根据官网适配手机情况做内容的删减；<br>2.引导注册：已经绑定小程序的用户引导注册进行隐藏；<br>3.视频模块：此处模块对接荟聚视频链接进行引用，点击播放，点击前部分视频需要做登录校验；<br>4.其他官网内容（如API接口文档）、非本期范围内容、二级页多层跳转等场景的小程序进行隐藏。</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;⚠️风险项：·普通小程序单个视频文件不能超过10MB；\n·内容审核合法合规包括服务类目与内容一致\n·如果视频是自制或独家内容，可能需要《信息网络传播视听节目许可证》&quot;}"><font color="#000000">⚠️风险项：·普通小程序单个视频文件不能超过10MB；<br>·内容审核合法合规包括服务类目与内容一致<br>·如果视频是自制或独家内容，可能需要《信息网络传播视听节目许可证》</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" rowspan=6 align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;活动&quot;}"><font color="#000000">活动</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329" rowspan=3 align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;线下活动&quot;}"><font color="#000000">线下活动</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;活动列表：线下活动展示（调用MA接口获得：活动名称|地址|时间|主图（图片尺寸？）|简息），小程序展示内容支持修改管理后台进行二次编辑&quot;}"><font color="#000000">活动列表：线下活动展示（调用MA接口获得：活动名称|地址|时间|主图（图片尺寸？）|简息），小程序展示内容支持修改管理后台进行二次编辑</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-right: 1px solid #1f2329" rowspan=3 align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;活动详情：1.跳转MA内嵌H5小程序；\n2.（小程序活动页）支持小程序端自己上传静态的活动页&quot;}"><font color="#1F2329">活动详情：1.跳转MA内嵌H5小程序；<br>2.（小程序活动页）支持小程序端自己上传静态的活动页</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;【管理后台】支持控制小程序前端是否展示，并根据推荐值展示顺序&quot;}"><font color="#000000">【管理后台】支持控制小程序前端是否展示，并根据推荐值展示顺序</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329" rowspan=2 align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;线上活动&quot;}"><font color="#000000">线上活动</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;线上活动展示（内嵌\/跳转视频号主页\/视频\/直播链接）&quot;}"><font color="#000000">线上活动展示（内嵌/跳转视频号主页/视频/直播链接）</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;https:\/\/developers.weixin.qq.com\/miniprogram\/dev\/framework\/open-ability\/channels-activity.html&quot;}"><font face="Calibri" color="#000000"><a href="https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/channels-activity.html">https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/channels-activity.html</a></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;【管理后台】获取视频号直播列表、视频列表，支持内嵌或跳转视频号。&quot;}"><font color="#000000">【管理后台】获取视频号直播列表、视频列表，支持内嵌或跳转视频号。</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;⚠️风险点：历史项目对接微信视频号直播跳转返回直播列表时接口报错，可采用内嵌视频号直播\/跳转视频号主页的方式进行跳转视频号直播&quot;}"><font color="#000000">⚠️风险点：历史项目对接微信视频号直播跳转返回直播列表时接口报错，可采用内嵌视频号直播/跳转视频号主页的方式进行跳转视频号直播</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;客服咨询入口&quot;}"><font color="#000000">客服咨询入口</font></td>
		<td style="border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;如用户需要咨询活动相关的信息，支持跳转调用公众号客服，&quot;}"><font color="#000000">如用户需要咨询活动相关的信息，支持跳转调用公众号客服，</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" rowspan=3 align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;干货知识&quot;}"><font color="#000000">干货知识</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;干货知识列表&quot;}"><font color="#000000">干货知识列表</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;支持干货内容搜索+干货标签筛选\n干货列表，排序与官网一致，标签关联&quot;}"><font color="#000000">支持干货内容搜索+干货标签筛选<br>干货列表，排序与官网一致，标签关联</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" rowspan=3 align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;根据更新频率及上线时间，讨论该模块可以前端直接开发？&quot;}"><font color="#1F2329">根据更新频率及上线时间，讨论该模块可以前端直接开发？</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;干货知识详情&quot;}"><font color="#000000">干货知识详情</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;展示视频组件、引导注册、已经注册的用户隐藏引导模块？&quot;}"><font color="#000000">展示视频组件、引导注册、已经注册的用户隐藏引导模块？</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;管理后台&quot;}"><font color="#000000">管理后台</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;【管理后台】列表根据推荐值进行排序，文章需要有【行业】【标签】进行筛选；筛选项需要在小程序端进行展示。&quot;}"><font color="#000000">【管理后台】列表根据推荐值进行排序，文章需要有【行业】【标签】进行筛选；筛选项需要在小程序端进行展示。</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" rowspan=8 align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;我的&quot;}"><font color="#000000">我的</font></td>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" rowspan=2 align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;注册、登录&quot;}"><font color="#000000">注册、登录</font></td>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;登录注册流程同上&quot;}"><font color="#000000">登录注册流程同上</font></td>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" rowspan=2 align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;【讨论】1个手机号 → 多个UnionID；1个UnionID → 多个手机号复杂场景下的用户流程。&quot;}"><font color="#000000">【讨论】1个手机号 → 多个UnionID；1个UnionID → 多个手机号复杂场景下的用户流程。</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;自动登录失效时长？&quot;}"><font color="#FF8800">自动登录失效时长？</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;解绑&quot;}"><font color="#000000">解绑</font></td>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;解除小程序与官网的关联关系&quot;}"><font color="#000000">解除小程序与官网的关联关系</font></td>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;是否本期可不做&quot;}"><font color="#000000">是否本期可不做</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;我的专属顾问&quot;}"><font color="#000000">我的专属顾问</font></td>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;通过union ID向MA查询该用户的绑定销售，需返回销售ID及带参企微码，用户扫码进入后根据参数发送指定欢迎语。&quot;}"><font color="#000000">通过union ID向MA查询该用户的绑定销售，需返回销售ID及带参企微码，用户扫码进入后根据参数发送指定欢迎语。</font></td>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;接口字段：销售codeID I员工企微ID I 客户ID I客户unionID I 带小程序参销售二维码&quot;}"><font color="#000000">接口字段：销售codeID I员工企微ID I 客户ID I客户unionID I 带小程序参销售二维码</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;我的活动&quot;}"><font color="#000000">我的活动</font></td>
		<td style="border-top: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;通过union ID向MA查询该用户已留资报名的线下活动类型的列表，点击跳转内嵌活动详情，由活动详情进一步查询活动进度。&quot;}"><font color="#000000">通过union ID向MA查询该用户已留资报名的线下活动类型的列表，点击跳转内嵌活动详情，由活动详情进一步查询活动进度。</font></td>
		<td style="border-top: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;MA接口返回该union ID用户匹配手机号所有的用户留资线下活动类型的信息，活动名称|地址|时间|主图|简息|活动详情地址链接（点击跳转活动详情）&quot;}"><font color="#000000">MA接口返回该union ID用户匹配手机号所有的用户留资线下活动类型的信息，活动名称|地址|时间|主图|简息|活动详情地址链接（点击跳转活动详情）</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;关注公众号&quot;}"><font color="#000000">关注公众号</font></td>
		<td style="border-top: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;点击跳转关注公众号二级页，长按识别二维码跳转公众号进行关注&quot;}"><font color="#000000">点击跳转关注公众号二级页，长按识别二维码跳转公众号进行关注</font></td>
		<td style="border-top: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;·制作一张引导关注公众号二级页&quot;}"><font color="#000000">·制作一张引导关注公众号二级页</font></td>
	</tr>
	<tr>
		<td style="border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;在线咨询&quot;}"><font color="#000000">在线咨询</font></td>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;跳转当前公众号客服H5链接，需提供跳转链接&quot;}"><font color="#000000">跳转当前公众号客服H5链接，需提供跳转链接</font></td>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;·跳转公众号客服H5链接&quot;}"><font color="#000000">·跳转公众号客服H5链接</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-left: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;隐私条款&quot;}"><font color="#000000">隐私条款</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;展示小程序授权的集团隐私政策和小程序隐私政策&quot;}"><font color="#000000">展示小程序授权的集团隐私政策和小程序隐私政策</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;·需提供集团隐私政策内容&quot;}"><font color="#000000">·需提供集团隐私政策内容</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" rowspan=13 height="846" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;管理后台&quot;}"><font color="#000000">管理后台</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;首页&quot;}"><font color="#000000">首页</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;数据概览&quot;}"><font color="#000000">数据概览</font></td>
		<td style="border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;数据概览：每日小程序PV \/ UV ； 注册人数 \/ 绑定人数 \/ 解绑人数&quot;}"><font color="#000000">数据概览：每日小程序PV / UV ； 注册人数 / 绑定人数 / 解绑人数</font></td>
		<td style="border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" rowspan=2 align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;首页管理&quot;}"><font color="#000000">首页管理</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;首页装修&quot;}"><font color="#000000">首页装修</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;支持根据业务需求配置小程序首页装修，装修组件：\n首页装修：支持配置图片、视频、轮播组件、富文本组件搭建首页\n跳转链接：支持跳转产品列表、活动列表、干货列表、注册页、自定义二级页\n模块配置项：支持控制首页模块可见范围：小程序已登录、小程序未登录&quot;}"><font color="#000000">支持根据业务需求配置小程序首页装修，装修组件：<br>首页装修：支持配置图片、视频、轮播组件、富文本组件搭建首页<br>跳转链接：支持跳转产品列表、活动列表、干货列表、注册页、自定义二级页<br>模块配置项：支持控制首页模块可见范围：小程序已登录、小程序未登录</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;自定义页面&quot;}"><font color="#000000">自定义页面</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;页面配置、装修&quot;}"><font color="#000000">页面配置、装修</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;产品管理&quot;}"><font color="#000000">产品管理</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;产品管理&quot;}"><font color="#000000">产品管理</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;产品分类、产品列表、产品详情页&quot;}"><font color="#000000">产品分类、产品列表、产品详情页</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;根据更新频率及上线时间，讨论该模块可以前端直接开发？&quot;}"><font color="#000000">根据更新频率及上线时间，讨论该模块可以前端直接开发？</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" rowspan=5 align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;活动管理&quot;}"><font color="#000000">活动管理</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;线下活动列表&quot;}"><font color="#000000">线下活动列表</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;管理小程序端展示的线下活动列表展示，支持线下活动的启用\/禁用，支持根据推荐值展示前端顺序，展示顺序：进行中—&gt;未开始—&gt;已结束。&quot;}"><font color="#000000">管理小程序端展示的线下活动列表展示，支持线下活动的启用/禁用，支持根据推荐值展示前端顺序，展示顺序：进行中—&gt;未开始—&gt;已结束。</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;线下活动详情&quot;}"><font color="#000000">线下活动详情</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;活动类型：1.同步MA：从MA获取活动名称|地址|时间|主图|简息I跳转活动链接；支持对同步的信息进行编辑；\n2.小程序活动：支持创建小程序自己的活动信息，并关联自定义二级页进行活动展示&quot;}"><font color="#000000">活动类型：1.同步MA：从MA获取活动名称|地址|时间|主图|简息I跳转活动链接；支持对同步的信息进行编辑；<br>2.小程序活动：支持创建小程序自己的活动信息，并关联自定义二级页进行活动展示</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;线上活动列表&quot;}"><font color="#000000">线上活动列表</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;管理小程序端展示的线上活动列表展示，支持线上活动的启用\/禁用，支持根据推荐值展示前端顺序，展示顺序：进行中—&gt;未开始—&gt;已结束。&quot;}"><font color="#000000">管理小程序端展示的线上活动列表展示，支持线上活动的启用/禁用，支持根据推荐值展示前端顺序，展示顺序：进行中—&gt;未开始—&gt;已结束。</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;线上活动详情&quot;}"><font color="#000000">线上活动详情</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;配置线上信息：活动名称|地址|时间|主图|简息I跳转活动链接；同步视频号的直播列表，视频号视频需要从视频号后台复制视频ID&quot;}"><font color="#000000">配置线上信息：活动名称|地址|时间|主图|简息I跳转活动链接；同步视频号的直播列表，视频号视频需要从视频号后台复制视频ID</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
	<tr>
		<td align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;活动banner管理&quot;}"><font color="#000000">活动banner管理</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;配置轮播图及跳转活动链接，支持直接跳转线上活动详情及线下活动详情。&quot;}"><font color="#000000">配置轮播图及跳转活动链接，支持直接跳转线上活动详情及线下活动详情。</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;干货管理&quot;}"><font color="#000000">干货管理</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;干货管理&quot;}"><font color="#000000">干货管理</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;案例分类、案例列表、案例详情页&quot;}"><font color="#000000">案例分类、案例列表、案例详情页</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;根据更新频率及上线时间，讨论该模块可以前端直接开发？&quot;}"><font color="#000000">根据更新频率及上线时间，讨论该模块可以前端直接开发？</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" rowspan=2 align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;活动带参链接管理&quot;}"><font color="#FF8800">活动带参链接管理</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;活动带参链接列表&quot;}"><font color="#1F2329">活动带参链接列表</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;链接列表、新建\/编辑\/删除\n新建小程序活动链接：活动参数名称、&quot;}"><font color="#1F2329">链接列表、新建/编辑/删除<br>新建小程序活动链接：活动参数名称、</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;同步逻辑&quot;}"><font color="#1F2329">同步逻辑</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;接收荟聚UTM\n创建小程序UTM\n传给官网UTM\nUTM的缓存时间：小程序自身缓存，人工干预处理：每日清理小程序缓存\/参数进来+24H\n小程序的UTM的数据：PV \/ UV是否需要传给数据中心平台&quot;}"><font color="#1F2329">接收荟聚UTM<br>创建小程序UTM<br>传给官网UTM<br>UTM的缓存时间：小程序自身缓存，人工干预处理：每日清理小程序缓存/参数进来+24H<br>小程序的UTM的数据：PV / UV是否需要传给数据中心平台</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;系统设置&quot;}"><font color="#000000">系统设置</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;订阅消息、账号、角色权限管理、日志&quot;}"><font color="#1F2329">订阅消息、账号、角色权限管理、日志</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" height="38" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;业务数据&quot;}"><font color="#000000">业务数据</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;小程序数据&quot;}"><font color="#000000">小程序数据</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;We分析-基础数据&quot;}"><font color="#000000">We分析-基础数据</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;页面、事件、属性、用户群体&quot;}"><font color="#000000">页面、事件、属性、用户群体</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=top data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" height="38" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;API\/接口对接&quot;}"><font color="#000000">API/接口对接</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;官网、MA、企业微信&quot;}"><font color="#000000">官网、MA、企业微信</font></td>
		<td style="border-top: 1px solid #1f2329; border-bottom: 1px solid #1f2329; border-left: 1px solid #1f2329; border-right: 1px solid #1f2329" align="left" valign=middle data-sheets-value="{ &quot;1&quot;: 2, &quot;2&quot;: &quot;&quot;}"><font color="#000000"><br></font></td>
	</tr>
</table>
<!-- ************************************************************************** -->
</body>

</html>
